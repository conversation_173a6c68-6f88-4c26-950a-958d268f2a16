#!/usr/bin/env python3
"""
Simple test script to debug the signal generation agent
"""

import asyncio
import sys
import traceback
from pathlib import Path

# Add the project root to the path
sys.path.append(str(Path(__file__).parent))

async def test_agent():
    try:
        print("Starting test...")
        
        # Import the agent
        from agents.options_signal_generation_agent import OptionsSignalGenerationAgent
        print("Agent imported successfully")
        
        # Create agent instance
        agent = OptionsSignalGenerationAgent()
        print("Agent instance created")
        
        # Try to initialize
        await agent.initialize()
        print("Agent initialized successfully")

        # Try to run one cycle
        print("Running one signal generation cycle...")

        # Set to batch mode for testing
        agent.config['mode'] = 'batch'
        agent.config['batch_cycles'] = 1

        await agent._run_signal_generation_cycle()
        print("Signal generation cycle completed")

        print("Test completed successfully!")
        
    except Exception as e:
        print(f"Error during test: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    print("Running signal agent test...")
    asyncio.run(test_agent())
